package com.example.pure.model.dto.request.log;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.Instant;

/**
 * 请求日志查询请求DTO
 * <p>
 * 用于接收前端的日志查询参数
 * </p>
 */
@Data
public class RequestLogQueryRequest {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 20;

    /**
     * 用户ID（可选）
     */
    private Long userId;

    /**
     * AI提供商（可选）
     */
    private String provider;

    /**
     * 请求状态（可选）
     */
    private String status;

    /**
     * 请求类型（可选）
     */
    private String requestType;

    /**
     * 模型名称（可选，支持模糊查询）
     */
    private String modelName;

    /**
     * 开始时间（可选）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Instant startTime;

    /**
     * 结束时间（可选）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Instant endTime;

    /**
     * 最小耗时（毫秒，可选）
     */
    private Long minDuration;

    /**
     * 最大耗时（毫秒，可选）
     */
    private Long maxDuration;

    /**
     * 是否只查询失败的请求
     */
    private Boolean onlyFailed = false;

    /**
     * 是否只查询慢请求（耗时超过5秒）
     */
    private Boolean onlySlow = false;

    /**
     * 排序字段（可选）
     * 支持：created_at, duration_ms, start_time
     */
    private String sortBy = "created_at";

    /**
     * 排序方向（可选）
     * 支持：asc, desc
     */
    private String sortOrder = "desc";
}
